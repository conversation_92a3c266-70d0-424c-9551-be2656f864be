<template>
  <div class="port-container">
    <!-- 端口信息悬浮框 -->
    <div class="port-tooltip" v-if="activePort" :style="tooltipStyle">
      <div class="tooltip-header">
        <span class="tooltip-title">端口 {{ activePort.portNumber }}</span>
        <span class="tooltip-status" :class="{'status-active': activePort.status === '0'}">
          {{ activePort.status === '1' ? '停用' : '正常' }}
        </span>
      </div>
      <div class="tooltip-content">
        <div class="tooltip-item">
          <span class="tooltip-label">端口类型:</span>
          <span class="tooltip-value">{{ activePort.portType || '未知' }}</span>
        </div>
        <div class="tooltip-item">
          <span class="tooltip-label">位置:</span>
          <span class="tooltip-value">X: {{ activePort.positionX || 0 }}, Y: {{ activePort.positionY || 0 }}</span>
        </div>
        <div class="tooltip-item" v-if="activePort.boardName">
          <span class="tooltip-label">所属板卡:</span>
          <span class="tooltip-value">{{ activePort.boardName }}</span>
        </div>
        <div class="tooltip-item" v-if="activePort.remarks">
          <span class="tooltip-label">备注:</span>
          <span class="tooltip-value">{{ activePort.remarks }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { EventBus } from '@/utils/eventBus';

export default {
  name: 'Port',
  data() {
    return {
      activePort: null, // 当前激活的端口
      mousePosition: { x: 0, y: 0 } // 鼠标位置
    };
  },
  computed: {
    tooltipStyle() {
      // 根据鼠标位置计算提示框样式
      return {
        left: (this.mousePosition.x + 10) + 'px',
        top: (this.mousePosition.y - 10) + 'px'
      };
    }
  },
  mounted() {
    // 添加鼠标移动事件监听
    document.addEventListener('mousemove', this.handleMouseMove);
  },
  methods: {
    // 显示端口信息
    showPortInfo(port, event) {
      this.activePort = port;
      if (event) {
        this.mousePosition = {
          x: event.clientX,
          y: event.clientY
        };
      }
    },

    // 隐藏端口信息
    hidePortInfo() {
      this.activePort = null;
    },

    // 监听鼠标移动
    handleMouseMove(event) {
      if (this.activePort) {
        this.mousePosition = {
          x: event.clientX,
          y: event.clientY
        };
      }
    },

    // 处理端口点击事件
    handlePortClick(port) {
      console.log('点击了端口:', port);
      // 发送端口点击事件，显示详细信息面板
      EventBus.$emit('port-clicked', port);
    },

    // 获取板卡的所有端口
    getBoardPorts(board) {
      if (!board || !board.rmEquipmentPortList) {
        return [];
      }
      return board.rmEquipmentPortList;
    },

    // 获取板卡左侧的端口（偶数索引）
    getLeftPorts(board) {
      const ports = this.getBoardPorts(board);
      return ports.filter((_port, index) => index % 2 === 0);
    },

    // 获取板卡右侧的端口（奇数索引）
    getRightPorts(board) {
      const ports = this.getBoardPorts(board);
      return ports.filter((_port, index) => index % 2 === 1);
    },

    // 渲染端口组件
    renderPorts(board, side = 'left') {
      if (!board) return [];

      const ports = side === 'left' ? this.getLeftPorts(board) : this.getRightPorts(board);

      return ports.map((port, index) => {
        return this.$createElement('div', {
          key: `${side}-port-${index}`,
          class: [
            'port-circle',
            {
              'port-active': port.status === '0',
              'port-inactive': port.status === '1'
            }
          ],
          style: {
            top: `${(index + 1) * (100 / (ports.length + 1))}%`
          },
          on: {
            mouseover: (event) => this.showPortInfo(port, event),
            mouseleave: () => this.hidePortInfo(),
            click: () => this.handlePortClick(port)
          }
        }, [
          this.$createElement('span', {
            class: 'port-number'
          }, port.portNumber || (index + 1))
        ]);
      });
    }
  },
  beforeDestroy() {
    // 移除鼠标移动事件监听
    document.removeEventListener('mousemove', this.handleMouseMove);
  },
  // 使用渲染函数来动态创建端口
  render(h) {
    return h('div', {
      class: 'port-container'
    }, [
      // 端口信息悬浮框
      this.activePort ? h('div', {
        class: 'port-tooltip',
        style: this.tooltipStyle
      }, [
        h('div', { class: 'tooltip-header' }, [
          h('span', { class: 'tooltip-title' }, `端口 ${this.activePort.portNumber}`),
          h('span', {
            class: ['tooltip-status', { 'status-active': this.activePort.status === '0' }]
          }, this.activePort.status === '1' ? '停用' : '正常')
        ]),
        h('div', { class: 'tooltip-content' }, [
          h('div', { class: 'tooltip-item' }, [
            h('span', { class: 'tooltip-label' }, '端口类型:'),
            h('span', { class: 'tooltip-value' }, this.activePort.portType || '未知')
          ]),
          h('div', { class: 'tooltip-item' }, [
            h('span', { class: 'tooltip-label' }, '位置:'),
            h('span', { class: 'tooltip-value' }, `X: ${this.activePort.positionX || 0}, Y: ${this.activePort.positionY || 0}`)
          ]),
          this.activePort.boardName ? h('div', { class: 'tooltip-item' }, [
            h('span', { class: 'tooltip-label' }, '所属板卡:'),
            h('span', { class: 'tooltip-value' }, this.activePort.boardName)
          ]) : null,
          this.activePort.remarks ? h('div', { class: 'tooltip-item' }, [
            h('span', { class: 'tooltip-label' }, '备注:'),
            h('span', { class: 'tooltip-value' }, this.activePort.remarks)
          ]) : null
        ].filter(Boolean))
      ]) : null
    ].filter(Boolean));
  }
};
</script>

<style scoped>
.port-container {
  position: relative;
  width: 100%;
  height: 100%;
}

/* 端口圆形样式 */
.port-circle {
  position: absolute;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: #666;
  border: 2px solid #333;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  justify-content: center;
  align-items: center;
  transform: translateY(-50%);
  z-index: 10;
}

.port-circle:hover {
  transform: translateY(-50%) scale(1.2);
  box-shadow: 0 0 10px rgba(57, 155, 218, 0.8);
  z-index: 15;
}

/* 端口状态样式 */
.port-active {
  background-color: #52c41a;
  border-color: #389e0d;
  box-shadow: 0 0 5px rgba(82, 196, 26, 0.5);
}

.port-inactive {
  background-color: #ff4d4f;
  border-color: #cf1322;
  box-shadow: 0 0 5px rgba(255, 77, 79, 0.5);
}

/* 端口编号样式 */
.port-number {
  color: #fff;
  font-size: 8px;
  font-weight: bold;
  text-shadow: 0 0 2px rgba(0, 0, 0, 0.8);
  pointer-events: none;
}

/* 提示框样式 */
.port-tooltip {
  position: fixed;
  background-color: rgba(0, 30, 60, 0.95);
  border: 2px solid rgba(57, 155, 218, 0.7);
  border-radius: 8px;
  padding: 12px;
  min-width: 200px;
  max-width: 300px;
  z-index: 1100;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.7);
  pointer-events: none;
  backdrop-filter: blur(5px);
  transform: translateY(-10px);
}

.tooltip-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  border-bottom: 1px solid rgba(57, 155, 218, 0.5);
  padding-bottom: 6px;
}

.tooltip-title {
  color: #ffffff;
  font-weight: bold;
  font-size: 14px;
  text-shadow: 0 0 3px rgba(0, 0, 0, 0.5);
}

.tooltip-status {
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 10px;
  background-color: rgba(255, 85, 0, 0.3);
  color: #ff7733;
  font-weight: bold;
}

.status-active {
  background-color: rgba(82, 196, 26, 0.3);
  color: #6dff3e;
}

.tooltip-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.tooltip-item {
  display: flex;
  flex-direction: column;
  background-color: rgba(0, 40, 80, 0.5);
  padding: 6px;
  border-radius: 4px;
  border-left: 2px solid rgba(57, 155, 218, 0.7);
}

.tooltip-label {
  color: rgba(255, 255, 255, 0.8);
  font-size: 12px;
  margin-bottom: 2px;
  font-weight: bold;
}

.tooltip-value {
  color: #ffffff;
  font-size: 13px;
}
</style>