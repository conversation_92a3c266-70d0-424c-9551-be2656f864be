<template>
  <div class="port-test-container">
    <h2>端口显示测试</h2>
    
    <!-- 模拟板卡 -->
    <div class="test-board">
      <h3>测试板卡</h3>
      
      <!-- 左侧端口 -->
      <div class="ports-left">
        <div
          v-for="(port, index) in leftPorts"
          :key="`left-port-${index}`"
          class="port-circle"
          :class="{
            'port-active': port.status === '0',
            'port-inactive': port.status === '1'
          }"
          :style="{
            top: `${(index + 1) * (100 / (leftPorts.length + 1))}%`
          }"
          @mouseover="(event) => showPortInfo(port, event)"
          @mouseleave="hidePortInfo"
          @click="() => handlePortClick(port)"
        >
          <span class="port-number">{{ port.portNumber }}</span>
        </div>
      </div>

      <!-- 板卡内容 -->
      <div class="board-content">
        <span class="board-label">测试板卡</span>
        <span class="port-count">{{ totalPorts }}端口</span>
      </div>

      <!-- 右侧端口 -->
      <div class="ports-right">
        <div
          v-for="(port, index) in rightPorts"
          :key="`right-port-${index}`"
          class="port-circle"
          :class="{
            'port-active': port.status === '0',
            'port-inactive': port.status === '1'
          }"
          :style="{
            top: `${(index + 1) * (100 / (rightPorts.length + 1))}%`
          }"
          @mouseover="(event) => showPortInfo(port, event)"
          @mouseleave="hidePortInfo"
          @click="() => handlePortClick(port)"
        >
          <span class="port-number">{{ port.portNumber }}</span>
        </div>
      </div>
    </div>

    <!-- 端口信息悬浮框 -->
    <div class="port-tooltip" v-if="activePort" :style="tooltipStyle">
      <div class="tooltip-header">
        <span class="tooltip-title">端口 {{ activePort.portNumber }}</span>
        <span class="tooltip-status" :class="{'status-active': activePort.status === '0'}">
          {{ activePort.status === '1' ? '停用' : '正常' }}
        </span>
      </div>
      <div class="tooltip-content">
        <div class="tooltip-item">
          <span class="tooltip-label">端口类型:</span>
          <span class="tooltip-value">{{ activePort.portType || '未知' }}</span>
        </div>
        <div class="tooltip-item">
          <span class="tooltip-label">位置:</span>
          <span class="tooltip-value">X: {{ activePort.positionX || 0 }}, Y: {{ activePort.positionY || 0 }}</span>
        </div>
        <div class="tooltip-item" v-if="activePort.boardName">
          <span class="tooltip-label">所属板卡:</span>
          <span class="tooltip-value">{{ activePort.boardName }}</span>
        </div>
      </div>
    </div>

    <!-- 端口详细信息面板 -->
    <DuanKou />
  </div>
</template>

<script>
import { EventBus } from '@/utils/eventBus';
import DuanKou from './DuanKou.vue';

export default {
  name: 'PortTest',
  components: {
    DuanKou
  },
  data() {
    return {
      activePort: null,
      mousePosition: { x: 0, y: 0 },
      // 模拟端口数据
      testPorts: [
        { portNumber: '1', portType: 'GE', status: '0', positionX: 1, positionY: 1, boardName: '测试板卡' },
        { portNumber: '2', portType: 'GE', status: '1', positionX: 1, positionY: 2, boardName: '测试板卡' },
        { portNumber: '3', portType: 'GE', status: '0', positionX: 1, positionY: 3, boardName: '测试板卡' },
        { portNumber: '4', portType: 'GE', status: '0', positionX: 1, positionY: 4, boardName: '测试板卡' },
        { portNumber: '5', portType: 'GE', status: '1', positionX: 1, positionY: 5, boardName: '测试板卡' },
        { portNumber: '6', portType: 'GE', status: '0', positionX: 1, positionY: 6, boardName: '测试板卡' },
        { portNumber: '7', portType: 'GE', status: '0', positionX: 1, positionY: 7, boardName: '测试板卡' },
        { portNumber: '8', portType: 'GE', status: '1', positionX: 1, positionY: 8, boardName: '测试板卡' }
      ]
    };
  },
  computed: {
    leftPorts() {
      return this.testPorts.filter((_, index) => index % 2 === 0);
    },
    rightPorts() {
      return this.testPorts.filter((_, index) => index % 2 === 1);
    },
    totalPorts() {
      return this.testPorts.length;
    },
    tooltipStyle() {
      return {
        left: (this.mousePosition.x + 10) + 'px',
        top: (this.mousePosition.y - 10) + 'px'
      };
    }
  },
  mounted() {
    document.addEventListener('mousemove', this.handleMouseMove);
  },
  methods: {
    showPortInfo(port, event) {
      this.activePort = port;
      if (event) {
        this.mousePosition = {
          x: event.clientX,
          y: event.clientY
        };
      }
    },
    hidePortInfo() {
      this.activePort = null;
    },
    handlePortClick(port) {
      console.log('点击了端口:', port);
      EventBus.$emit('port-clicked', port);
    },
    handleMouseMove(event) {
      if (this.activePort) {
        this.mousePosition = {
          x: event.clientX,
          y: event.clientY
        };
      }
    }
  },
  beforeDestroy() {
    document.removeEventListener('mousemove', this.handleMouseMove);
  }
};
</script>

<style scoped>
.port-test-container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.test-board {
  width: 200px;
  height: 400px;
  background-color: #f0f0f0;
  border: 2px solid #aaa;
  border-radius: 8px;
  position: relative;
  margin: 20px auto;
  display: flex;
  justify-content: center;
  align-items: center;
}

.board-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
}

.board-label {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.port-count {
  font-size: 12px;
  color: #666;
}

/* 端口容器样式 */
.ports-left, .ports-right {
  position: absolute;
  top: 0;
  height: 100%;
  width: 15px;
  z-index: 20;
}

.ports-left {
  left: -8px;
}

.ports-right {
  right: -8px;
}

/* 端口圆形样式 */
.port-circle {
  position: absolute;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: #666;
  border: 2px solid #333;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  justify-content: center;
  align-items: center;
  transform: translateY(-50%);
  z-index: 25;
}

.port-circle:hover {
  transform: translateY(-50%) scale(1.3);
  box-shadow: 0 0 10px rgba(57, 155, 218, 0.8);
  z-index: 30;
}

/* 端口状态样式 */
.port-active {
  background-color: #52c41a;
  border-color: #389e0d;
  box-shadow: 0 0 5px rgba(82, 196, 26, 0.5);
}

.port-inactive {
  background-color: #ff4d4f;
  border-color: #cf1322;
  box-shadow: 0 0 5px rgba(255, 77, 79, 0.5);
}

/* 端口编号样式 */
.port-number {
  color: #fff;
  font-size: 8px;
  font-weight: bold;
  text-shadow: 0 0 2px rgba(0, 0, 0, 0.8);
  pointer-events: none;
}

/* 端口提示框样式 */
.port-tooltip {
  position: fixed;
  background-color: rgba(0, 30, 60, 0.95);
  border: 2px solid rgba(57, 155, 218, 0.7);
  border-radius: 8px;
  padding: 12px;
  min-width: 200px;
  max-width: 300px;
  z-index: 1100;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.7);
  pointer-events: none;
  backdrop-filter: blur(5px);
  transform: translateY(-10px);
}

.tooltip-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  border-bottom: 1px solid rgba(57, 155, 218, 0.5);
  padding-bottom: 6px;
}

.tooltip-title {
  color: #ffffff;
  font-weight: bold;
  font-size: 14px;
  text-shadow: 0 0 3px rgba(0, 0, 0, 0.5);
}

.tooltip-status {
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 10px;
  background-color: rgba(255, 85, 0, 0.3);
  color: #ff7733;
  font-weight: bold;
}

.status-active {
  background-color: rgba(82, 196, 26, 0.3);
  color: #6dff3e;
}

.tooltip-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.tooltip-item {
  display: flex;
  flex-direction: column;
  background-color: rgba(0, 40, 80, 0.5);
  padding: 6px;
  border-radius: 4px;
  border-left: 2px solid rgba(57, 155, 218, 0.7);
}

.tooltip-label {
  color: rgba(255, 255, 255, 0.8);
  font-size: 12px;
  margin-bottom: 2px;
  font-weight: bold;
}

.tooltip-value {
  color: #ffffff;
  font-size: 13px;
}
</style>
